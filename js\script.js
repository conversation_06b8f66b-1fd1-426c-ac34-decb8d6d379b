'use strict';

/**
 * Todo类 - 表示单个待办事项
 */
class Todo {
    constructor(text, id = null) {
        this.id = id || this.generateId();
        this.text = text.trim();
        this.completed = false;
        this.createdAt = new Date();
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    toggle() {
        this.completed = !this.completed;
    }

    updateText(newText) {
        this.text = newText.trim();
    }
}

/**
 * TodoApp类 - 管理整个应用状态
 */
class TodoApp {
    constructor() {
        this.todos = [];
        this.currentFilter = 'all';
        this.editingId = null;
        
        this.initializeElements();
        this.bindEvents();
        this.loadFromStorage();
        this.render();
    }

    initializeElements() {
        // 表单和输入元素
        this.todoForm = document.getElementById('todoForm');
        this.todoInput = document.getElementById('todoInput');
        
        // 过滤按钮
        this.filterButtons = document.querySelectorAll('.filter-btn');
        
        // 列表和状态元素
        this.todoList = document.getElementById('todoList');
        this.emptyState = document.getElementById('emptyState');
        
        // 统计和操作元素
        this.allCount = document.getElementById('allCount');
        this.activeCount = document.getElementById('activeCount');
        this.completedCount = document.getElementById('completedCount');
        this.totalStats = document.getElementById('totalStats');
        this.clearCompletedBtn = document.getElementById('clearCompleted');
        
        // 模态框元素
        this.confirmModal = document.getElementById('confirmModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalMessage = document.getElementById('modalMessage');
        this.modalConfirm = document.getElementById('modalConfirm');
        this.modalCancel = document.getElementById('modalCancel');
    }

    bindEvents() {
        // 表单提交事件
        this.todoForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTodo();
        });

        // 过滤按钮事件
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setFilter(btn.dataset.filter);
            });
        });

        // 清除已完成按钮事件
        this.clearCompletedBtn.addEventListener('click', () => {
            this.showConfirmModal(
                '清除已完成项目',
                '确定要删除所有已完成的待办事项吗？此操作无法撤销。',
                () => this.clearCompleted()
            );
        });

        // 模态框事件
        this.modalCancel.addEventListener('click', () => this.hideConfirmModal());
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) {
                this.hideConfirmModal();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.cancelEdit();
                this.hideConfirmModal();
            }
        });
    }

    addTodo() {
        const text = this.todoInput.value.trim();
        if (!text) return;

        const todo = new Todo(text);
        this.todos.unshift(todo);
        this.todoInput.value = '';
        this.saveToStorage();
        this.render();
        
        // 聚焦到输入框
        this.todoInput.focus();
    }

    deleteTodo(id) {
        const index = this.todos.findIndex(todo => todo.id === id);
        if (index > -1) {
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            if (todoElement) {
                todoElement.classList.add('removing');
                setTimeout(() => {
                    this.todos.splice(index, 1);
                    this.saveToStorage();
                    this.render();
                }, 300);
            }
        }
    }

    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            todo.toggle();
            this.saveToStorage();
            this.render();
        }
    }

    startEdit(id) {
        this.cancelEdit(); // 取消其他编辑
        this.editingId = id;
        this.render();
        
        // 聚焦到编辑输入框
        const editInput = document.querySelector(`[data-id="${id}"] .todo-edit-input`);
        if (editInput) {
            editInput.focus();
            editInput.select();
        }
    }

    saveEdit(id, newText) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo && newText.trim()) {
            todo.updateText(newText);
            this.editingId = null;
            this.saveToStorage();
            this.render();
        }
    }

    cancelEdit() {
        this.editingId = null;
        this.render();
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        this.filterButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
            btn.setAttribute('aria-selected', btn.dataset.filter === filter);
        });
        
        this.render();
    }

    clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
        this.saveToStorage();
        this.render();
        this.hideConfirmModal();
    }

    getFilteredTodos() {
        switch (this.currentFilter) {
            case 'active':
                return this.todos.filter(todo => !todo.completed);
            case 'completed':
                return this.todos.filter(todo => todo.completed);
            default:
                return this.todos;
        }
    }

    updateCounts() {
        const total = this.todos.length;
        const completed = this.todos.filter(todo => todo.completed).length;
        const active = total - completed;

        this.allCount.textContent = total;
        this.activeCount.textContent = active;
        this.completedCount.textContent = completed;
        this.totalStats.textContent = `总计: ${total} 项`;

        // 显示/隐藏清除按钮
        this.clearCompletedBtn.style.display = completed > 0 ? 'block' : 'none';
    }

    createTodoElement(todo) {
        const li = document.createElement('li');
        li.className = `todo-item ${todo.completed ? 'completed' : ''}`;
        li.setAttribute('data-id', todo.id);
        li.setAttribute('role', 'listitem');

        const isEditing = this.editingId === todo.id;

        li.innerHTML = `
            <div class="todo-checkbox ${todo.completed ? 'checked' : ''}" 
                 role="checkbox" 
                 aria-checked="${todo.completed}"
                 tabindex="0"
                 aria-label="标记为${todo.completed ? '未完成' : '已完成'}">
            </div>
            <span class="todo-text ${isEditing ? 'editing' : ''}">${this.escapeHtml(todo.text)}</span>
            <input type="text" 
                   class="todo-edit-input ${isEditing ? 'active' : ''}" 
                   value="${this.escapeHtml(todo.text)}"
                   maxlength="200">
            <div class="todo-actions">
                ${isEditing ? `
                    <button class="action-btn save-btn" aria-label="保存编辑">✓</button>
                    <button class="action-btn cancel-btn" aria-label="取消编辑">✕</button>
                ` : `
                    <button class="action-btn edit-btn" aria-label="编辑待办事项">✏️</button>
                    <button class="action-btn delete-btn" aria-label="删除待办事项">🗑️</button>
                `}
            </div>
        `;

        this.bindTodoEvents(li, todo);
        return li;
    }

    bindTodoEvents(element, todo) {
        const checkbox = element.querySelector('.todo-checkbox');
        const editBtn = element.querySelector('.edit-btn');
        const deleteBtn = element.querySelector('.delete-btn');
        const saveBtn = element.querySelector('.save-btn');
        const cancelBtn = element.querySelector('.cancel-btn');
        const editInput = element.querySelector('.todo-edit-input');
        const todoText = element.querySelector('.todo-text');

        // 复选框事件
        checkbox.addEventListener('click', () => this.toggleTodo(todo.id));
        checkbox.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTodo(todo.id);
            }
        });

        // 编辑相关事件
        if (editBtn) {
            editBtn.addEventListener('click', () => this.startEdit(todo.id));
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.showConfirmModal(
                    '删除待办事项',
                    `确定要删除"${todo.text}"吗？`,
                    () => this.deleteTodo(todo.id)
                );
            });
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveEdit(todo.id, editInput.value);
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelEdit());
        }

        // 双击编辑
        todoText.addEventListener('dblclick', () => {
            if (!todo.completed) {
                this.startEdit(todo.id);
            }
        });

        // 编辑输入框事件
        if (editInput) {
            editInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.saveEdit(todo.id, editInput.value);
                } else if (e.key === 'Escape') {
                    this.cancelEdit();
                }
            });

            editInput.addEventListener('blur', () => {
                // 延迟执行，避免与按钮点击冲突
                setTimeout(() => {
                    if (this.editingId === todo.id) {
                        this.saveEdit(todo.id, editInput.value);
                    }
                }, 100);
            });
        }
    }

    render() {
        const filteredTodos = this.getFilteredTodos();
        
        // 清空列表
        this.todoList.innerHTML = '';
        
        // 显示/隐藏空状态
        if (filteredTodos.length === 0) {
            this.emptyState.classList.remove('hidden');
            this.todoList.style.display = 'none';
        } else {
            this.emptyState.classList.add('hidden');
            this.todoList.style.display = 'flex';
            
            // 渲染待办事项
            filteredTodos.forEach(todo => {
                const todoElement = this.createTodoElement(todo);
                this.todoList.appendChild(todoElement);
            });
        }
        
        // 更新统计
        this.updateCounts();
    }

    showConfirmModal(title, message, onConfirm) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.confirmModal.classList.add('active');
        this.confirmModal.setAttribute('aria-hidden', 'false');
        
        // 移除之前的事件监听器
        this.modalConfirm.replaceWith(this.modalConfirm.cloneNode(true));
        this.modalConfirm = document.getElementById('modalConfirm');
        
        // 添加新的事件监听器
        this.modalConfirm.addEventListener('click', () => {
            onConfirm();
        });
        
        // 聚焦到取消按钮
        this.modalCancel.focus();
    }

    hideConfirmModal() {
        this.confirmModal.classList.remove('active');
        this.confirmModal.setAttribute('aria-hidden', 'true');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    saveToStorage() {
        try {
            localStorage.setItem('todos', JSON.stringify(this.todos));
        } catch (error) {
            console.error('保存到本地存储失败:', error);
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('todos');
            if (stored) {
                const data = JSON.parse(stored);
                this.todos = data.map(item => {
                    const todo = new Todo(item.text, item.id);
                    todo.completed = item.completed;
                    todo.createdAt = new Date(item.createdAt);
                    return todo;
                });
            }
        } catch (error) {
            console.error('从本地存储加载失败:', error);
            this.todos = [];
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});
