/* CSS重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 */
:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    
    --bg-color: #ffffff;
    --surface-color: #f9fafb;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    
    --transition: all 0.2s ease-in-out;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #111827;
        --surface-color: #1f2937;
        --border-color: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
    }
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: var(--transition);
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 2rem;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* 输入区域样式 */
.input-section {
    margin-bottom: 2rem;
}

.input-group {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
}

.todo-input {
    flex: 1;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background-color: var(--surface-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.todo-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(79 70 229 / 0.1);
}

.add-btn {
    padding: 0.875rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.add-btn:active {
    transform: translateY(0);
}

/* 过滤按钮样式 */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    background-color: var(--surface-color);
    padding: 0.25rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.filter-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.filter-btn:not(.active):hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.count {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.filter-btn:not(.active) .count {
    background-color: var(--border-color);
    color: var(--text-muted);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-bottom: 2rem;
}

.todo-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 待办事项样式 */
.todo-item {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    animation: slideIn 0.3s ease-out;
}

.todo-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.todo-item.completed {
    opacity: 0.7;
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

/* 复选框样式 */
.todo-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
    flex-shrink: 0;
}

.todo-checkbox.checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.todo-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

/* 文本内容 */
.todo-text {
    flex: 1;
    font-size: 1rem;
    line-height: 1.5;
    word-break: break-word;
}

.todo-text.editing {
    display: none;
}

.todo-edit-input {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-sm);
    font-size: 1rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    display: none;
}

.todo-edit-input.active {
    display: block;
}

/* 操作按钮 */
.todo-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.todo-item:hover .todo-actions {
    opacity: 1;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
}

.edit-btn {
    background-color: var(--warning-color);
    color: white;
}

.edit-btn:hover {
    background-color: #d97706;
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
}

.delete-btn:hover {
    background-color: #dc2626;
}

.save-btn {
    background-color: var(--success-color);
    color: white;
}

.cancel-btn {
    background-color: var(--secondary-color);
    color: white;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* 底部样式 */
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.stats {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.clear-completed-btn {
    padding: 0.5rem 1rem;
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.clear-completed-btn:hover {
    background-color: #dc2626;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.todo-item.removing {
    animation: slideOut 0.3s ease-out forwards;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .container {
        padding: 1rem 0.75rem;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .filter-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .filter-btn {
        justify-content: space-between;
    }
    
    .footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .todo-actions {
        opacity: 1;
    }
}
